import MijickPopups
import SwiftUI

struct AuthenticationView: View {
    @EnvironmentObject var viewModel: AuthenticationController
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack(spacing: 0) {
            AuthNavigationBar(
                onClose: {
                    presentationMode.wrappedValue.dismiss()
                },
                backgroundColor: DesignSystem.Colors.whiteBlack
            )

            ScrollView {
                VStack(spacing: DesignSystem.Spacing.xl) {
                    AuthenticationHeader(
                        title: viewModel.currentFlow == .login ? "欢迎回来" : "创建账号",
                        subtitle: viewModel
                            .currentFlow == .login ? "登录您的MoonvyAI账号，继续您的AI创作之旅" : "注册MoonvyAI账号，开启您的AI创作之旅"
                    )
                    .padding(.top, DesignSystem.Spacing.xl)

                    VStack(spacing: DesignSystem.Spacing.lg) {
                        if viewModel.currentFlow == .login {
                            LoginView(viewModel: viewModel, onShowWebView: showWebView)
                        } else {
                            RegisterView(viewModel: viewModel, onShowWebView: showWebView)
                        }

                        flowSwitcher
                    }
                    .padding(.horizontal, DesignSystem.Spacing.xl)

                    Spacer(minLength: DesignSystem.Spacing.xl)
                }
            }
        }
        .background(DesignSystem.Colors.whiteBlack)
        .navigationBarHidden(true)
        .withToast()
        .onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
    }

    private func showWebView(url: String, title: String) {
        guard let webURL = URL(string: url) else { return }
        Task {
            await WebViewPopup(url: webURL, title: title).present()
        }
    }

    private var flowSwitcher: some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            Text(viewModel.currentFlow == .login ? "还没有账号？" : "已有账号？")
                .bodyCompactFixedStyle()
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Text(viewModel.currentFlow == .login ? "立即注册" : "立即登录")
                .bodyCompactFixedStyle()
                .foregroundColor(viewModel.isAnyLoading ? DesignSystem.Colors.textSecondary : DesignSystem.Colors
                    .primary
                )
                .onTapGesture {
                    if !viewModel.isAnyLoading {
                        viewModel.switchFlow()
                    }
                }
        }
    }
}
