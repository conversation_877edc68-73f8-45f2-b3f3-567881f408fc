import Combine
import SwiftUI

enum LoadingOperation: Equatable {
    case login
    case register
    case sendCode
    case checkUserExists
    case socialAuth(SocialAuthProvider)
}

class AuthenticationController: ObservableObject {
    @Published var authState: AuthenticationState = .notAuthenticated
    @Published var currentFlow: AuthenticationFlow = .login

    @Published var loginEmail = ""
    @Published var loginPassword = ""

    @Published var registerEmail = ""
    @Published var registerPassword = ""
    @Published var verificationCode = ""
    @Published var isAgreementAccepted = false

    @Published var isShowingAuthentication = false

    // UI验证错误状态
    @Published var loginEmailError: String? = nil
    @Published var loginPasswordError: String? = nil
    @Published var registerEmailError: String? = nil
    @Published var registerPasswordError: String? = nil

    // 操作加载状态
    @Published var currentLoadingOperation: LoadingOperation?

    // 便捷计算属性
    var isLoginLoading: Bool { currentLoadingOperation == .login }
    var isRegisterLoading: Bool { currentLoadingOperation == .register }
    var isSendCodeLoading: Bool { currentLoadingOperation == .sendCode }
    var isCheckUserExistsLoading: Bool { currentLoadingOperation == .checkUserExists }
    func isSocialAuthLoading(_ provider: SocialAuthProvider) -> Bool {
        if case .socialAuth(let currentProvider) = currentLoadingOperation {
            return currentProvider == provider
        }
        return false
    }

    var isAnyLoading: Bool { currentLoadingOperation != nil }

    private let authStore = AuthStore.shared
    private let loginService = LoginService()
    private let checkUserExistsService = CheckUserExistsService()
    private let registerService = RegisterService()
    private let sendVerificationCodeService = SendVerificationCodeService()
    private var cancellables = Set<AnyCancellable>()
    private var notificationObserver: NSObjectProtocol?

    init() {
        setupAuthStateObservation()
        setupAutoCloseOnSuccess()

        // TODO: 实际生产环境要移除
        if AppConfig.Environment.current.isDevelopment {
            loginEmail = AppConfig.Auth.defaultEmail
            loginPassword = AppConfig.Auth.defaultPassword
            isAgreementAccepted = true
        }
    }

    private func setupAuthStateObservation() {
        authStore.$user
            .map { user in
                if user.accessToken != nil {
                    return .authenticated
                } else {
                    return .notAuthenticated
                }
            }
            .assign(to: \.authState, on: self)
            .store(in: &cancellables)
    }

    // MARK: - 认证操作

    func showAuthentication(flow: AuthenticationFlow = .login) {
        currentFlow = flow
        isShowingAuthentication = true
        clearError()
    }

    func hideAuthentication() {
        isShowingAuthentication = false
        clearForm()
    }

    func switchFlow() {
        currentFlow = currentFlow == .login ? .register : .login
        clearUIValidationErrors()
        clearError()
    }

    // MARK: - 登录操作

    @MainActor
    func performLogin() async {
        if loginEmail.isEmpty {
            return
        }

        if loginPassword.isEmpty {
            return
        }

        if !isAgreementAccepted {
            authState = .error(.agreementNotAccepted)
            return
        }

        guard validateLoginInput() else { return }

        currentLoadingOperation = .login

        do {
            let request = LoginReq(email: loginEmail, password: loginPassword)
            _ = try await login(req: request)
            authState = .authenticated

            ToastManager.shared.showSuccess("欢迎回来！")
            hideAuthentication()
            NotificationCenter.default.post(name: NSNotification.Name("AuthenticationSuccess"), object: nil)
            NotificationCenter.default.post(name: .userAuthenticationReady, object: nil)

        } catch {
            let authError = mapError(error)
            authState = .error(authError)
            ToastManager.shared.showError(authError.errorDescription ?? "登录失败，请重试")
        }

        currentLoadingOperation = nil
    }

    // MARK: - 注册操作

    @MainActor
    func performRegister() async {
        if registerEmail.isEmpty {
            ToastManager.shared.showError("请输入邮箱地址")
            return
        }

        if registerPassword.isEmpty {
            ToastManager.shared.showError("请输入密码")
            return
        }

        if verificationCode.isEmpty {
            ToastManager.shared.showError("请输入验证码")
            return
        }

        if !isAgreementAccepted {
            authState = .error(.agreementNotAccepted)
            ToastManager.shared.showError("请先阅读并同意用户协议和隐私协议")
            return
        }

        guard validateRegisterInput() else { return }

        currentLoadingOperation = .register

        do {
            let registerRequest = RegisterReq(
                email: registerEmail,
                password: registerPassword,
                terminal: Terminal.ios.rawValue,
                locale: Language.deviceLanguage,
                code: verificationCode
            )

            let registerRes = try await registerService.register(req: registerRequest)

            guard registerRes.code == 200 else {
                throw BusinessError(code: registerRes.code, message: registerRes.msg)
            }

            let loginRequest = LoginReq(email: registerEmail, password: registerPassword)
            _ = try await login(req: loginRequest)

            authState = .authenticated

            ToastManager.shared.showSuccess("注册成功，欢迎加入MoonvyAI！")
            hideAuthentication()
            NotificationCenter.default.post(name: NSNotification.Name("AuthenticationSuccess"), object: nil)
            NotificationCenter.default.post(name: .userAuthenticationReady, object: nil)

        } catch {
            let authError = mapError(error)
            authState = .error(authError)
            ToastManager.shared.showError(authError.errorDescription ?? "注册失败，请重试")
        }

        currentLoadingOperation = nil
    }

    @MainActor
    func checkUserExistsAndSendVerificationCode() async {
        if registerEmail.isEmpty {
            ToastManager.shared.showError("请输入邮箱地址")
            return
        }

        guard validateEmail(registerEmail) else {
            authState = .error(.invalidEmail)
            ToastManager.shared.showError("请输入有效的邮箱地址")
            return
        }

        currentLoadingOperation = .checkUserExists

        do {
            let userExistsData = try await checkUserExistsService.checkUserExists(email: registerEmail)
            if userExistsData.exists {
                currentLoadingOperation = nil

                let message = "该邮箱已存在，您可立即登录"

                authState = .error(.serverError(message))
                ToastManager.shared.showInfo(message)
                return
            }

            await sendVerificationCodeInternal()

        } catch {
            currentLoadingOperation = nil
            let authError = mapError(error)
            authState = .error(authError)
            ToastManager.shared.showError("检查失败")
        }
    }

    @MainActor
    private func sendVerificationCodeInternal() async {
        currentLoadingOperation = .sendCode

        do {
            let response = try await sendVerificationCodeService.sendVerificationCode(
                email: registerEmail,
                emailType: .register
            )

            if response.code == 200 {
                authState = .notAuthenticated
                ToastManager.shared.showSuccess("验证码已发送至邮箱")
            } else {
                authState = .error(.networkError)
                ToastManager.shared.showError(response.msg.isEmpty ? "验证码发送失败，请重试" : response.msg)
            }
        } catch {
            let authError = mapError(error)
            authState = .error(authError)
            ToastManager.shared.showError(authError.errorDescription ?? "验证码发送失败，请重试")
        }

        currentLoadingOperation = nil
    }

    // MARK: - 社交认证

    @MainActor
    private lazy var appleSignInService = AppleSignInService()

    @MainActor
    private lazy var googleSignInService = GoogleSignInService()

    @MainActor
    func performSocialAuth(provider: SocialAuthProvider) async {
        currentLoadingOperation = .socialAuth(provider)

        do {
            let socialAuthResponse: SocialAuthResponse = switch provider {
            case .apple:
                try await appleSignInService.signInWithApple()
            case .google:
                try await googleSignInService.signInWithGoogle()
            }

            let loginRes = LoginRes(
                accessToken: socialAuthResponse.accessToken,
                refreshToken: socialAuthResponse.refreshToken,
                memberLevel: socialAuthResponse.memberLevel,
                unionId: socialAuthResponse.unionId,
                expireDate: socialAuthResponse.expireDate
            )

            AuthStore.shared.updateUser(user: loginRes)
            authState = .authenticated

            ToastManager.shared.showSuccess("欢迎回来！")
            hideAuthentication()
            NotificationCenter.default.post(name: NSNotification.Name("AuthenticationSuccess"), object: nil)
            NotificationCenter.default.post(name: .userAuthenticationReady, object: nil)

        } catch let error as SocialAuthError {
            if case .authorizationCancelled = error {
                authState = .notAuthenticated
            } else {
                let authError = mapSocialAuthError(error)
                authState = .error(authError)
                ToastManager.shared.showError(authError.errorDescription ?? "社交登录失败")
            }
        } catch {
            authState = .error(.networkError)
            ToastManager.shared.showError("社交登录失败，请重试")
        }

        currentLoadingOperation = nil
    }

    // MARK: - 公开的验证方法（供 View 使用）

    func isValidEmail(_ email: String) -> Bool {
        return ValidationUtils.isValidEmail(email)
    }

    func isValidPassword(_ password: String) -> Bool {
        return ValidationUtils.isValidPassword(password)
    }

    // 登录表单是否有效
    var isLoginFormValid: Bool {
        isValidEmail(loginEmail) && isValidPassword(loginPassword) && isAgreementAccepted
    }

    // 注册表单是否有效
    var isRegisterFormValid: Bool {
        isValidEmail(registerEmail) &&
            isValidPassword(registerPassword) &&
            !verificationCode.isEmpty &&
            isAgreementAccepted
    }

    // 是否可以发送验证码
    var canSendVerificationCode: Bool {
        isValidEmail(registerEmail)
    }

    // MARK: - UI验证方法

    func validateLoginEmailForUI() {
        loginEmailError = ValidationUtils.getEmailValidationError(loginEmail)
    }

    func validateLoginPasswordForUI() {
        loginPasswordError = ValidationUtils.getPasswordValidationError(loginPassword)
    }

    func validateRegisterEmailForUI() {
        registerEmailError = ValidationUtils.getEmailValidationError(registerEmail)
    }

    func validateRegisterPasswordForUI() {
        registerPasswordError = ValidationUtils.getPasswordValidationError(registerPassword)
    }

    // MARK: - 验证

    @MainActor
    private func validateLoginInput() -> Bool {
        if !validateEmail(loginEmail) {
            authState = .error(.invalidEmail)
            ToastManager.shared.showError("请输入有效的邮箱地址")
            return false
        }

        if loginPassword.isEmpty {
            authState = .error(.invalidPassword)
            ToastManager.shared.showError("请输入密码")
            return false
        }

        return true
    }

    @MainActor
    private func validateRegisterInput() -> Bool {
        if !validateEmail(registerEmail) {
            authState = .error(.invalidEmail)
            ToastManager.shared.showError("请输入有效的邮箱地址")
            return false
        }

        if registerPassword.count < 8 {
            authState = .error(.passwordTooShort)
            ToastManager.shared.showError("密码至少需要8个字符")
            return false
        }

        if verificationCode.isEmpty {
            authState = .error(.invalidVerificationCode)
            ToastManager.shared.showError("请输入验证码")
            return false
        }

        if !isAgreementAccepted {
            authState = .error(.agreementNotAccepted)
            ToastManager.shared.showError("请先阅读并同意用户协议和隐私协议")
            return false
        }

        return true
    }

    private func validateEmail(_ email: String) -> Bool {
        return ValidationUtils.isValidEmail(email)
    }

    // MARK: - 工具方法

    private func clearUIValidationErrors() {
        loginEmailError = nil
        loginPasswordError = nil
        registerEmailError = nil
        registerPasswordError = nil
    }

    private func clearForm() {
        loginEmail = ""
        loginPassword = ""
        registerEmail = ""
        registerPassword = ""
        verificationCode = ""
        isAgreementAccepted = false

        clearUIValidationErrors()
        clearError()
    }

    private func clearError() {
        if case .error = authState {
            authState = .notAuthenticated
        }
    }

    private func mapError(_ error: Error) -> AuthError {
        if let authError = error as? AuthError {
            return authError
        }

        if let businessError = error as? BusinessError {
            return .serverError(businessError.message)
        }

        return .unknown
    }

    private func mapSocialAuthError(_ error: SocialAuthError) -> AuthError {
        switch error {
        case .authorizationCancelled:
            return .unknown
        case .appleSignInFailed(let message):
            return .socialAuthError("Apple登录失败: \(message)")
        case .googleSignInFailed(let message):
            return .socialAuthError("Google登录失败: \(message)")
        case .networkError:
            return .networkError
        case .invalidResponse:
            return .serverError("服务器响应无效")
        case .missingCredentials:
            return .socialAuthError("缺少必要的认证信息")
        }
    }

    // 登录
    func login(req: LoginReq) async throws -> LoginRes {
        let loginRes = try await loginService.login(req: req)

        await MainActor.run {
            AuthStore.shared.updateUser(user: loginRes)
        }

        return loginRes
    }

    // MARK: - 认证检查

    func checkAuthenticationRequired() -> Bool {
        return !AuthStore.shared.hasValidToken()
    }

    func requireAuthentication() {
        if checkAuthenticationRequired() {
            showAuthentication()
        }
    }

    /// 异步认证检查，用于业务控制器
    @MainActor
    func ensureAuthenticated() async -> Bool {
        if checkAuthenticationRequired() {
            showAuthentication()
            return false
        }
        return true
    }

    /// 同步认证检查，抛出错误用于业务逻辑
    func requireAuthenticationOrThrow() throws {
        if checkAuthenticationRequired() {
            throw AuthError.notAuthenticated
        }
    }

    deinit {
        removeNotificationObserver()
    }
}

extension AuthenticationController {
    func setupAutoCloseOnSuccess() {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("AuthenticationRequired"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAuthenticationRequired()
        }
    }

    func removeNotificationObserver() {
        NotificationCenter.default.removeObserver(
            self,
            name: NSNotification.Name("AuthenticationRequired"),
            object: nil
        )
    }

    /// 处理认证失效（401响应）
    private func handleAuthenticationRequired() {
        print("\(DateUtils.formatTimeOnly()) ⚠️ 收到认证失效通知，尝试token刷新")

        Task {
            do {
                _ = try await AuthStore.shared.validToken()
                print("\(DateUtils.formatTimeOnly()) ✅ 认证失效处理：token刷新成功，保持登录状态")

                await MainActor.run {
                    NotificationCenter.default.post(name: .userAuthenticationReady, object: nil)
                }

            } catch {
                print("\(DateUtils.formatTimeOnly()) ❌ 认证失效处理：token刷新失败 - \(error)")

                await MainActor.run {
                    AuthStore.shared.clearUser()
                    authState = .notAuthenticated
                    showAuthentication()
                }
            }
        }
    }
}
